{"version": 3, "file": "static/css/main.36f3b76a.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,2BAAmB,CAAnB,yBAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,SAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,6BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,+BAAmB,EAAnB,kEAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,sBAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,wLAAmB,CAEnB,KAEI,mIAKJ,CAEA,KACI,uEAEJ,CCdA,MAEE,yBAA0B,CAC1B,2BAA4B,CAC5B,0BAA2B,CAC3B,wBAAyB,CACzB,8BAA+B,CAC/B,6BAA8B,CAC9B,+BAA8C,CAC9C,yBAA0B,CAC1B,+BAAgC,CAChC,yBAA0B,CAC1B,uBAAwB,CACxB,sBAA0B,CAC1B,kCAAmC,CACnC,+BAAgC,CAChC,8BAA+B,CAC/B,8BAA+B,CAC/B,gCAAiC,CACjC,4BAA6B,CAC7B,4BAA6B,CAC7B,wBAAyB,CACzB,8BAA+B,CAC/B,8BAA+B,CAG/B,yDAAqF,CACrF,iEAA4F,CAC5F,kEAA8F,CAC9F,sEAAkG,CAClG,qEAAqF,CACrF,uDAAmF,CACnF,4DAAwF,CAGxF,0DAAqE,CACrE,yDAAoE,CACpE,0DAAqE,CACrE,oDAAkE,CAClE,6DAAwG,CACxG,0DAAqE,CAGrE,oFAA0F,CAC1F,qEAA0E,CAC1E,2CAA8C,CAG9C,kBAAmB,CACnB,iBAAkB,CAClB,oBAAqB,CACrB,cAAe,CACf,kBAAmB,CACnB,kBAAmB,CACnB,iBAAkB,CAClB,mBAAoB,CACpB,kBAAmB,CAGnB,iBAAkB,CAClB,gBAAiB,CACjB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAGhB,iDAAqD,CACrD,mDAAuD,CACvD,iDAAqD,CACrD,4DACF,CAGA,EAEE,qBAAsB,CADtB,6EAAgC,CAAhC,+BAEF,CAEA,KACE,cAAe,CACf,sBACF,CAEA,KACE,2EAAiF,CAEjF,kCAAmC,CACnC,iCAAkC,CAGlC,kBAAwC,CAAxC,uCAAwC,CADxC,aAAkC,CAAlC,iCAAkC,CADlC,eAAgB,CAGhB,QAAS,CACT,SAAU,CAPV,iCAQF,CAGA,aAGE,sFAAoG,CACpG,iBAA2B,CAA3B,0BAA2B,CAH3B,gBAAiB,CACjB,UAGF,CAEA,oBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAAkC,CAAlC,iCAAkC,CAElC,+BAAoD,CAApD,mDAAoD,CACpD,oDAA8B,CAA9B,6BAA8B,CAE9B,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CAFN,UAAW,CAGX,UACF,CAQA,uDALE,cAAe,CAEf,iBAAuC,CAAvC,sCAAuC,CADvC,UAQF,CAEA,eACE,WACF,CAEA,eAGE,aAAkC,CAAlC,iCAAkC,CAGlC,sCAAgC,CAAhC,+BAAgC,CALhC,kBAA0B,CAA1B,yBAA0B,CAC1B,eAAgB,CAEhB,sBAAwB,CACxB,eAEF,CAEA,kBAEE,aAAoC,CAApC,mCAAoC,CADpC,cAAyB,CAAzB,wBAAyB,CAEzB,eAAgB,CAEhB,qBAAuB,CACvB,eAAgB,CAFhB,gBAA0B,CAA1B,yBAGF,CAEA,2BAEE,kBAAmB,CADnB,YAAa,CAEb,QAAmB,CAAnB,kBACF,CAEA,+CAGE,kBAAmB,CAEnB,kBAA6B,CAA7B,4BAA6B,CAH7B,YAAa,CAKb,gBAAyB,CAAzB,wBAAyB,CADzB,eAAgB,CAEhB,qBAAuB,CAJvB,sBAAsC,CAAtC,qCAAsC,CAKtC,uCAAoC,CAApC,mCAAoC,CACpC,kBACF,CAEA,sBACE,kDAAmC,CAAnC,kCAAmC,CAEnC,4DAAgC,CAAhC,+BAAgC,CADhC,UAEF,CAEA,4BAEE,8DAA+B,CAA/B,8BAA+B,CAD/B,0BAEF,CAEA,yBAIE,mCAAoC,CAHpC,kDAAmC,CAAnC,kCAAmC,CAEnC,4DAAgC,CAAhC,+BAAgC,CADhC,UAGF,CAGA,cAIE,eAAwB,CAAxB,uBAAwB,CAKxB,iBAAkB,CARlB,YAAa,CAIb,wDAEe,CALf,qCAAsC,CACtC,4BAA6B,CAK7B,eAEF,CAEA,mBACE,cAAe,CACf,eACF,CAEA,qBACE,gBAAiB,CACjB,eACF,CAEA,uBACE,kBAAmB,CACnB,eACF,CAEA,kBACE,aAAc,CACd,iBAA0B,CAA1B,yBACF,CAGA,cAOE,kCAA2B,CAA3B,0BAA2B,CAN3B,+CAAgC,CAAhC,+BAAgC,CAGhC,wBAA6C,CAA7C,4CAA6C,CAF7C,kBAA6B,CAA7B,4BAA6B,CAC7B,oDAA8B,CAA9B,6BAA8B,CAK9B,gBAAiB,CAHjB,eAAgB,CAIhB,iBAAkB,CAHlB,uCAAoC,CAApC,mCAIF,CAEA,oBACE,qDAAkC,CAAlC,iCAAkC,CAClC,0BACF,CAEA,qBAOE,qDAAyF,CAAzF,yEAAyF,CANzF,UAAW,CAKX,UAAW,CAFX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,gBAGE,kCAA2B,CAA3B,0BAA2B,CAF3B,sDAAkC,CAAlC,iCAAkC,CAClC,+BAAoD,CAApD,mDAAoD,CAEpD,YAAuB,CAAvB,sBACF,CAGA,gBAME,kBAAmB,CAHnB,kDAAkC,CAAlC,iCAAkC,CAClC,qBAA6B,CAA7B,4BAA6B,CAK7B,4DAAgC,CAAhC,+BAAgC,CADhC,UAAY,CAHZ,YAAa,CAHb,WAAuB,CAAvB,sBAAuB,CAKvB,sBAAuB,CAGvB,uCAAoC,CAApC,mCAAoC,CATpC,UAAsB,CAAtB,qBAUF,CAEA,sBAEE,8DAA+B,CAA/B,8BAA+B,CAD/B,qBAEF,CAEA,sBAEE,kBAAmB,CAEnB,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAGnB,aAAc,CARd,YAAa,CAMb,iBAAmB,CACnB,eAAgB,CALhB,sBAOF,CAEA,YAGE,kBAAmB,CACnB,iBAAkB,CAFlB,cAAgB,CAGhB,mBAAqB,CAJrB,aAKF,CAEA,sCACE,SACF,CAEA,4CACE,kBAAwC,CAAxC,uCAAwC,CACxC,iBACF,CAEA,4CACE,kBAAqC,CAArC,oCAAqC,CACrC,iBAAkB,CAClB,UACF,CAEA,kDACE,UACF,CAEA,sBACE,kDAAkC,CAAlC,iCAAkC,CAKlC,oBAA6B,CAA7B,4BAA6B,CAJ7B,4DAAgC,CAAhC,+BAKF,CAEA,0CANE,iBAA2B,CAA3B,0BAA2B,CAE3B,eAAgB,CADhB,sBAAsC,CAAtC,qCAaF,CARA,oBACE,kDAAsG,CAAtG,kGAAsG,CACtG,wBAA6C,CAA7C,4CAA6C,CAK7C,oBAA6B,CAA7B,4BAA6B,CAJ7B,sDAA8B,CAA9B,6BAKF,CAEA,qBAKE,kDAAmD,CAFnD,kBAAqC,CAArC,oCAAqC,CACrC,iBAAkB,CAFlB,YAAc,CADd,WAKF,CAEA,yBAGE,kCAA2B,CAA3B,0BAA2B,CAF3B,sDAAkC,CAAlC,iCAAkC,CAClC,4BAAiD,CAAjD,gDAAiD,CAEjD,YAAuB,CAAvB,sBACF,CAEA,eAKE,eAAkC,CAAlC,iCAAkC,CAFlC,wBAAuC,CAAvC,sCAAuC,CACvC,qBAA6B,CAA7B,4BAA6B,CAM7B,sDAA8B,CAA9B,6BAA8B,CAJ9B,aAAkC,CAAlC,iCAAkC,CAClC,cAAyB,CAAzB,wBAAyB,CACzB,eAAgB,CAGhB,eAAgB,CAThB,sBAAsC,CAAtC,qCAAsC,CAOtC,uCAAkC,CAAlC,iCAAkC,CARlC,UAWF,CAEA,qBAEE,oBAAmC,CAAnC,kCAAmC,CACnC,iDAA8B,CAA9B,6BAA8B,CAF9B,YAAa,CAGb,0BACF,CAEA,4BACE,aAAgC,CAAhC,+BAAgC,CAChC,eACF,CAEA,qBAEE,kDAAkC,CAAlC,iCAAkC,CAElC,WAAY,CACZ,qBAA6B,CAA7B,4BAA6B,CAI7B,4DAAgC,CAAhC,+BAAgC,CANhC,UAAY,CAOZ,cAAe,CAHf,iBAA2B,CAA3B,0BAA2B,CAD3B,eAAgB,CAKhB,eAAgB,CAVhB,oBAAsC,CAAtC,qCAAsC,CAOtC,uCAAkC,CAAlC,iCAIF,CAEA,0CAEE,8DAA+B,CAA/B,8BAA+B,CAD/B,0BAEF,CAEA,8BAEE,kBAAmB,CADnB,UAEF,CAGA,qBAEE,kBAAmB,CAEnB,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAGnB,aAA6B,CAA7B,4BAA6B,CAR7B,YAAa,CAMb,cAAe,CACf,eAAgB,CALhB,qBAOF,CAEA,0BAEE,kDAA6D,CAC7D,wBAAyB,CACzB,oBAAsB,CACtB,kBAAmB,CAJnB,eAKF,CAEA,0BAEE,kDAA6D,CAC7D,wBAAuC,CAAvC,sCAAuC,CACvC,oBAAsB,CAHtB,eAIF,CAEA,6BAEE,kDAA6D,CAC7D,wBAAyB,CACzB,oBAAsB,CACtB,eAAgB,CAJhB,eAKF,CAEA,wBACE,eAAkC,CAAlC,iCAAkC,CAClC,wBAA6C,CAA7C,4CAA6C,CAC7C,mBAAqB,CAGrB,sDAA8B,CAA9B,6BAA8B,CAD9B,kBAAmB,CADnB,YAAa,CAGb,uCAAoC,CAApC,mCACF,CAEA,8BACE,4DAAgC,CAAhC,+BAAgC,CAChC,0BACF,CAEA,mCACE,eACF,CAEA,uBAGE,aAAkC,CAAlC,iCAAkC,CAFlC,kBAAmB,CACnB,eAAgB,CAEhB,sBAAwB,CACxB,oBACF,CAEA,qBACE,YAAa,CACb,qBAAsB,CACtB,SAAW,CACX,kBACF,CAEA,eAGE,aAAkC,CAAlC,iCAAkC,CAFlC,iBAAmB,CACnB,eAAgB,CAEhB,qBACF,CAEA,kBAEE,aAAgC,CAAhC,+BAAgC,CADhC,gBAAkB,CAElB,iBACF,CAEA,sCAKE,eAAkC,CAAlC,iCAAkC,CAFlC,wBAAuC,CAAvC,sCAAuC,CACvC,mBAAqB,CAMrB,sDAA8B,CAA9B,6BAA8B,CAJ9B,aAAkC,CAAlC,iCAAkC,CAClC,iBAAmB,CACnB,eAAgB,CANhB,mBAAqB,CAOrB,uCAAkC,CAAlC,iCAEF,CAEA,kDAGE,oBAAmC,CAAnC,kCAAmC,CACnC,iDAA8B,CAA9B,6BAA8B,CAF9B,YAGF,CAEA,0BAEE,kBAAmB,CAGnB,eAAkC,CAAlC,iCAAkC,CAClC,wBAAuC,CAAvC,sCAAuC,CACvC,mBAAqB,CACrB,sDAA8B,CAA9B,6BAA8B,CAP9B,YAAa,CAEb,6BAA8B,CAM9B,kBAAmB,CALnB,oBAMF,CAEA,qBACE,WACF,CAEA,gBAEE,oBAAqB,CAErB,cAAe,CAHf,iBAAkB,CAElB,UAEF,CAEA,uBAOE,kBAAiC,CAAjC,gCAAiC,CAEjC,qBAAsB,CAHtB,QAAS,CAIT,oCAA8C,CAR9C,cAAe,CAEf,MAAO,CACP,OAAQ,CAFR,KAQF,CAEA,qDAZE,iBAAkB,CAOlB,uCAAoC,CAApC,mCAgBF,CAXA,8BAOE,eAAiB,CAEjB,iBAAkB,CAHlB,aAAe,CAIf,sDAA8B,CAA9B,6BAA8B,CAR9B,UAAW,CACX,cAAe,CAEf,WAAa,CADb,aAOF,CAEA,qDACE,kBAAiC,CAAjC,gCACF,CAEA,4DACE,6BACF,CAGA,sBACE,iBACF,CAEA,mBACE,YAAa,CACb,UACF,CAEA,qBAEE,kDAA6D,CAC7D,wBAAyB,CACzB,mBAAqB,CAGrB,aAAiC,CAAjC,gCACF,CAEA,2CALE,gBAAkB,CAClB,eAAgB,CALhB,kBAiBF,CARA,sBAEE,kDAA6D,CAC7D,wBAAyB,CACzB,mBAAqB,CAGrB,aAAoC,CAApC,mCACF,CAEA,mBAGE,sDAA+F,CAC/F,oBAAsB,CAHtB,eAAgB,CAChB,YAAa,CAGb,iBACF,CAEA,qBAEE,aAAgC,CAAhC,+BAAgC,CADhC,iBAAmB,CAEnB,eACF,CAEA,kBAGE,kBAAmB,CADnB,YAAa,CADb,gBAGF,CAEA,qBACE,UACF,CAEA,oBACE,aAAgC,CAAhC,+BAAgC,CAChC,UACF,CAEA,2BAGE,kDAA6D,CAC7D,wBAAyB,CACzB,kBAAmB,CAJnB,oBAAqB,CAMrB,eAAgB,CALhB,cAAe,CAIf,eAEF,CAGA,oBAEE,kBAAmB,CAEnB,kDAA6D,CAC7D,wBAAuC,CAAvC,sCAAuC,CACvC,mBAAqB,CAErB,aAAoC,CAApC,mCAAoC,CAPpC,YAAa,CAMb,iBAAmB,CAJnB,kBAMF,CAEA,yBAIE,eAAkC,CAAlC,iCAAkC,CADlC,wBAAuC,CAAvC,sCAAuC,CADvC,kBAAmB,CADnB,eAIF,CAEA,eAEE,wBAAyB,CADzB,UAEF,CAEA,kBAEE,kDAA6D,CAC7D,+BAA8C,CAA9C,6CAA8C,CAG9C,aAAkC,CAAlC,iCAAkC,CADlC,eAAgB,CAGhB,oBAAsB,CADtB,eAAgB,CAEhB,wBACF,CAEA,oCARE,iBAAmB,CAHnB,oBAiBF,CANA,kBAEE,+BAAoD,CAApD,mDAAoD,CAEpD,aAAoC,CAApC,mCAAoC,CACpC,kBACF,CAEA,wBACE,sDACF,CAEA,sBAIE,kDAA6D,CAC7D,wBAAuC,CAAvC,sCAAuC,CAEvC,aAAkC,CAAlC,iCAAkC,CANlC,+DAA6B,CAA7B,4BAA6B,CAE7B,aAMF,CAEA,8CALE,mBAAqB,CAJrB,gBAAkB,CAMlB,eAWF,CARA,wBACE,oBAAqB,CAKrB,oBAAsB,CAJtB,sBAAyB,CAKzB,wBACF,CAGA,yBACE,MAEE,SAAU,CADV,kBAEF,CACA,IAEE,UAAY,CADZ,qBAEF,CACF,CAEA,0BACE,UACE,kBACF,CACA,IACE,kBACF,CACF,CAEA,yBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAGA,0BACE,cAEE,aAAwB,CAAxB,uBAAwB,CADxB,qCAEF,CACF,CAEA,0BACE,cAEE,eAAwB,CAAxB,uBAAwB,CADxB,qCAEF,CAEA,uDAEE,qBAAuC,CAAvC,sCACF,CAEA,eACE,gBAA0B,CAA1B,yBACF,CACF,CAEA,0BACE,cAOE,QAAS,CALT,oDAIO,CALP,yBAOF,CAEA,+DAGE,cACF,CAEA,eACE,cACF,CAEA,2BACE,qBAAsB,CACtB,UACF,CAEA,uDAEE,gBACF,CACF,CAEA,yBAKE,uDACE,cACF,CAEA,eACE,iBACF,CAEA,kBACE,cACF,CAEA,cACE,UACF,CAEA,cACE,qBAAsB,CACtB,eACF,CAEA,gBACE,wBACF,CAOA,6EACE,cACF,CACF,CAGA,iBAOE,kBAAsB,CAEtB,QAAS,CANT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAOlB,kBAAmB,CANnB,SAQF,CAEA,oBACE,uCAAoC,CAApC,mCACF,CAEA,qBACE,iDAA8B,CAA9B,6BACF,CAGA,aACE,aACE,yBACF,CAEA,cAEE,kCAAoC,CADpC,yBAEF,CAEA,UACE,sBACF,CACF,CAGA,+BACE,MACE,qBAAyB,CACzB,6BACF,CACF,CAGA,uCACE,EACE,kCAAqC,CACrC,qCAAuC,CAEvC,8BAAgC,CADhC,mCAEF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\nbody {\r\n    margin: 0;\r\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\",\r\n        \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\",\r\n        \"Helvetica Neue\", sans-serif;\r\n    -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n    font-family: source-code-pro, Menlo, Monaco, Consolas, \"Courier New\",\r\n        monospace;\r\n}\r\n", "/* Premium Wire Shelves 3D Configurator Styles */\r\n\r\n:root {\r\n  /* Enhanced Premium Color Palette - High Quality */\r\n  --premium-primary: #0f172a;\r\n  --premium-secondary: #1e293b;\r\n  --premium-tertiary: #334155;\r\n  --premium-accent: #4f46e5;\r\n  --premium-accent-light: #6366f1;\r\n  --premium-accent-dark: #3730a3;\r\n  --premium-accent-glow: rgba(79, 70, 229, 0.15);\r\n  --premium-success: #10b981;\r\n  --premium-success-light: #34d399;\r\n  --premium-warning: #f59e0b;\r\n  --premium-error: #ef4444;\r\n  --premium-surface: #ffffff;\r\n  --premium-surface-elevated: #fefefe;\r\n  --premium-surface-muted: #f8fafc;\r\n  --premium-surface-card: #fdfdfd;\r\n  --premium-text-primary: #0f172a;\r\n  --premium-text-secondary: #374151;\r\n  --premium-text-muted: #6b7280;\r\n  --premium-text-light: #9ca3af;\r\n  --premium-border: #e5e7eb;\r\n  --premium-border-light: #f3f4f6;\r\n  --premium-border-muted: #f9fafb;\r\n  \r\n  /* Enhanced Premium Shadows */\r\n  --shadow-soft: 0 1px 3px 0 rgba(15, 23, 42, 0.08), 0 1px 2px 0 rgba(15, 23, 42, 0.04);\r\n  --shadow-medium: 0 4px 6px -1px rgba(15, 23, 42, 0.1), 0 2px 4px -1px rgba(15, 23, 42, 0.06);\r\n  --shadow-large: 0 10px 15px -3px rgba(15, 23, 42, 0.12), 0 4px 6px -2px rgba(15, 23, 42, 0.05);\r\n  --shadow-premium: 0 20px 25px -5px rgba(15, 23, 42, 0.15), 0 10px 10px -5px rgba(15, 23, 42, 0.04);\r\n  --shadow-glow: 0 0 0 1px var(--premium-accent-glow), 0 0 20px rgba(79, 70, 229, 0.12);\r\n  --shadow-card: 0 8px 32px rgba(15, 23, 42, 0.08), 0 4px 16px rgba(15, 23, 42, 0.04);\r\n  --shadow-floating: 0 16px 64px rgba(15, 23, 42, 0.12), 0 8px 32px rgba(15, 23, 42, 0.06);\r\n\r\n  /* Enhanced Premium Gradients */\r\n  --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);\r\n  --gradient-accent: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);\r\n  --gradient-surface: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);\r\n  --gradient-card: linear-gradient(145deg, #ffffff 0%, #fdfdfd 100%);\r\n  --gradient-header: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);\r\n  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n  \r\n  /* Enhanced Typography System */\r\n  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  --font-mono: 'JetBrains Mono', 'SF Mono', 'Fira Code', Consolas, monospace;\r\n  --font-display: 'Inter', system-ui, sans-serif;\r\n\r\n  /* Reduced Font Sizes - More Compact */\r\n  --text-xs: 0.625rem;   /* 10px */\r\n  --text-sm: 0.75rem;    /* 12px */\r\n  --text-base: 0.875rem; /* 14px */\r\n  --text-lg: 1rem;       /* 16px */\r\n  --text-xl: 1.125rem;   /* 18px */\r\n  --text-2xl: 1.25rem;   /* 20px */\r\n  --text-3xl: 1.5rem;    /* 24px */\r\n  --text-4xl: 1.875rem;  /* 30px */\r\n  --text-5xl: 2.25rem;   /* 36px */\r\n\r\n  /* Enhanced Spacing System */\r\n  --space-1: 0.25rem;    /* 4px */\r\n  --space-2: 0.5rem;     /* 8px */\r\n  --space-3: 0.75rem;    /* 12px */\r\n  --space-4: 1rem;       /* 16px */\r\n  --space-5: 1.25rem;    /* 20px */\r\n  --space-6: 1.5rem;     /* 24px */\r\n  --space-8: 2rem;       /* 32px */\r\n  --space-10: 2.5rem;    /* 40px */\r\n  --space-12: 3rem;      /* 48px */\r\n  --space-16: 4rem;      /* 64px */\r\n  --space-20: 5rem;      /* 80px */\r\n\r\n  /* Premium Animations */\r\n  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);\r\n  --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);\r\n  --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);\r\n  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);\r\n}\r\n\r\n/* Enhanced Global Styles */\r\n* {\r\n  font-family: var(--font-primary);\r\n  box-sizing: border-box;\r\n}\r\n\r\nhtml {\r\n  font-size: 16px;\r\n  scroll-behavior: smooth;\r\n}\r\n\r\nbody {\r\n  font-feature-settings: 'kern' 1, 'liga' 1, 'frac' 1, 'cv02' 1, 'cv03' 1, 'cv04' 1;\r\n  text-rendering: optimizeLegibility;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  line-height: 1.6;\r\n  color: var(--premium-text-primary);\r\n  background: var(--premium-surface-muted);\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n/* Enhanced Premium App Layout */\r\n.premium-app {\r\n  min-height: 100vh;\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 25%, #f1f5f9 50%, #f8fafc 75%, #fefefe 100%);\r\n  font-size: var(--text-base);\r\n}\r\n\r\n.premium-app-header {\r\n  background: var(--gradient-header);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--premium-border-light);\r\n  box-shadow: var(--shadow-card);\r\n  width: 100%;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 50;\r\n}\r\n\r\n.premium-app-header .max-w-7xl {\r\n  max-width: none;\r\n  width: 100%;\r\n  padding: var(--space-8) var(--space-12);\r\n}\r\n\r\n.premium-app > .max-w-7xl {\r\n  max-width: none;\r\n  width: 100%;\r\n  padding: var(--space-8) var(--space-12);\r\n}\r\n\r\n.premium-brand {\r\n  flex-grow: 1;\r\n}\r\n\r\n.premium-title {\r\n  font-size: var(--text-4xl);\r\n  font-weight: 800;\r\n  color: var(--premium-text-primary);\r\n  letter-spacing: -0.025em;\r\n  line-height: 1.1;\r\n  font-family: var(--font-display);\r\n}\r\n\r\n.premium-subtitle {\r\n  font-size: var(--text-lg);\r\n  color: var(--premium-text-secondary);\r\n  font-weight: 500;\r\n  margin-top: var(--space-2);\r\n  letter-spacing: -0.01em;\r\n  line-height: 1.5;\r\n}\r\n\r\n.premium-status-indicators {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-4);\r\n}\r\n\r\n.premium-indicator-ai,\r\n.premium-indicator-ready {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--space-4);\r\n  font-weight: 600;\r\n  font-size: var(--text-sm);\r\n  letter-spacing: 0.025em;\r\n  transition: var(--transition-normal);\r\n  white-space: nowrap;\r\n}\r\n\r\n.premium-indicator-ai {\r\n  background: var(--gradient-primary);\r\n  color: white;\r\n  box-shadow: var(--shadow-medium);\r\n}\r\n\r\n.premium-indicator-ai:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--shadow-large);\r\n}\r\n\r\n.premium-indicator-ready {\r\n  background: var(--gradient-success);\r\n  color: white;\r\n  box-shadow: var(--shadow-medium);\r\n  animation: premium-pulse 2s infinite;\r\n}\r\n\r\n/* Enhanced Premium Grid Layout - Compact */\r\n.premium-grid {\r\n  display: grid;\r\n  grid-template-columns: 400px 1fr 380px;\r\n  grid-template-rows: auto auto;\r\n  grid-gap: var(--space-6);\r\n  grid-template-areas:\r\n    \"chat viewer controls\"\r\n    \"bom bom bom\";\r\n  min-height: 80vh;\r\n  align-items: start;\r\n}\r\n\r\n.premium-grid-chat {\r\n  grid-area: chat;\r\n  min-width: 400px;\r\n}\r\n\r\n.premium-grid-viewer {\r\n  grid-area: viewer;\r\n  min-width: 600px;\r\n}\r\n\r\n.premium-grid-controls {\r\n  grid-area: controls;\r\n  min-width: 380px;\r\n}\r\n\r\n.premium-grid-bom {\r\n  grid-area: bom;\r\n  margin-top: var(--space-6);\r\n}\r\n\r\n/* Enhanced Premium Card Interface */\r\n.premium-card {\r\n  background: var(--gradient-card);\r\n  border-radius: var(--space-4);\r\n  box-shadow: var(--shadow-card);\r\n  border: 1px solid var(--premium-border-light);\r\n  overflow: hidden;\r\n  transition: var(--transition-normal);\r\n  backdrop-filter: blur(20px);\r\n  min-height: 500px;\r\n  position: relative;\r\n}\r\n\r\n.premium-card:hover {\r\n  box-shadow: var(--shadow-floating);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.premium-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, transparent, var(--premium-accent-light), transparent);\r\n  opacity: 0.6;\r\n}\r\n\r\n.premium-header {\r\n  background: var(--gradient-header);\r\n  border-bottom: 1px solid var(--premium-border-light);\r\n  backdrop-filter: blur(20px);\r\n  padding: var(--space-8);\r\n}\r\n\r\n/* Enhanced Premium Avatar and Spacing */\r\n.premium-avatar {\r\n  width: var(--space-16);\r\n  height: var(--space-16);\r\n  background: var(--gradient-accent);\r\n  border-radius: var(--space-5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-medium);\r\n  transition: var(--transition-normal);\r\n}\r\n\r\n.premium-avatar:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: var(--shadow-large);\r\n}\r\n\r\n.premium-status-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0.75rem 1.25rem;\r\n  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);\r\n  border: 1px solid #bbf7d0;\r\n  border-radius: 1rem;\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  color: #059669;\r\n}\r\n\r\n.status-dot {\r\n  width: 0.625rem;\r\n  height: 0.625rem;\r\n  background: #10b981;\r\n  border-radius: 50%;\r\n  margin-right: 0.75rem;\r\n}\r\n\r\n.premium-scrollbar::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.premium-scrollbar::-webkit-scrollbar-track {\r\n  background: var(--premium-surface-muted);\r\n  border-radius: 4px;\r\n}\r\n\r\n.premium-scrollbar::-webkit-scrollbar-thumb {\r\n  background: var(--premium-text-muted);\r\n  border-radius: 4px;\r\n  opacity: 0.5;\r\n}\r\n\r\n.premium-scrollbar::-webkit-scrollbar-thumb:hover {\r\n  opacity: 0.8;\r\n}\r\n\r\n.premium-user-message {\r\n  background: var(--gradient-accent);\r\n  box-shadow: var(--shadow-medium);\r\n  font-size: var(--text-base);\r\n  padding: var(--space-5) var(--space-6);\r\n  line-height: 1.6;\r\n  border-radius: var(--space-6);\r\n}\r\n\r\n.premium-ai-message {\r\n  background: linear-gradient(135deg, var(--premium-surface-card) 0%, var(--premium-surface-muted) 100%);\r\n  border: 1px solid var(--premium-border-light);\r\n  box-shadow: var(--shadow-soft);\r\n  font-size: var(--text-base);\r\n  padding: var(--space-5) var(--space-6);\r\n  line-height: 1.6;\r\n  border-radius: var(--space-6);\r\n}\r\n\r\n.premium-loading-dot {\r\n  width: 0.5rem;\r\n  height: 0.5rem;\r\n  background: var(--premium-text-muted);\r\n  border-radius: 50%;\r\n  animation: premium-bounce 1.4s infinite ease-in-out;\r\n}\r\n\r\n.premium-input-container {\r\n  background: var(--gradient-header);\r\n  border-top: 1px solid var(--premium-border-light);\r\n  backdrop-filter: blur(20px);\r\n  padding: var(--space-8);\r\n}\r\n\r\n.premium-input {\r\n  width: 100%;\r\n  padding: var(--space-5) var(--space-6);\r\n  border: 2px solid var(--premium-border);\r\n  border-radius: var(--space-5);\r\n  background: var(--premium-surface);\r\n  color: var(--premium-text-primary);\r\n  font-size: var(--text-lg);\r\n  font-weight: 500;\r\n  transition: var(--transition-fast);\r\n  box-shadow: var(--shadow-soft);\r\n  line-height: 1.5;\r\n}\r\n\r\n.premium-input:focus {\r\n  outline: none;\r\n  border-color: var(--premium-accent);\r\n  box-shadow: var(--shadow-glow);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.premium-input::placeholder {\r\n  color: var(--premium-text-muted);\r\n  font-weight: 400;\r\n}\r\n\r\n.premium-send-button {\r\n  padding: var(--space-5) var(--space-8);\r\n  background: var(--gradient-accent);\r\n  color: white;\r\n  border: none;\r\n  border-radius: var(--space-5);\r\n  font-weight: 600;\r\n  font-size: var(--text-base);\r\n  transition: var(--transition-fast);\r\n  box-shadow: var(--shadow-medium);\r\n  cursor: pointer;\r\n  min-width: 120px;\r\n}\r\n\r\n.premium-send-button:hover:not(:disabled) {\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--shadow-large);\r\n}\r\n\r\n.premium-send-button:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Premium Parameter Controls - More Spacious */\r\n.premium-ready-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0.75rem 1.5rem;\r\n  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);\r\n  border: 1px solid #a7f3d0;\r\n  border-radius: 1rem;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: var(--premium-success);\r\n}\r\n\r\n.premium-section-required {\r\n  padding: 1.25rem;\r\n  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\r\n  border: 1px solid #bfdbfe;\r\n  border-radius: 0.75rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.premium-section-optional {\r\n  padding: 1.25rem;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  border: 1px solid var(--premium-border);\r\n  border-radius: 0.75rem;\r\n}\r\n\r\n.premium-section-accessories {\r\n  padding: 1.25rem;\r\n  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);\r\n  border: 1px solid #d8b4fe;\r\n  border-radius: 0.75rem;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.premium-accessory-card {\r\n  background: var(--premium-surface);\r\n  border: 1px solid var(--premium-border-light);\r\n  border-radius: 0.5rem;\r\n  padding: 1rem;\r\n  margin-bottom: 1rem;\r\n  box-shadow: var(--shadow-soft);\r\n  transition: var(--transition-normal);\r\n}\r\n\r\n.premium-accessory-card:hover {\r\n  box-shadow: var(--shadow-medium);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.premium-accessory-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.premium-section-title {\r\n  font-size: 1.375rem;\r\n  font-weight: 700;\r\n  color: var(--premium-text-primary);\r\n  letter-spacing: -0.025em;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.premium-input-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.premium-label {\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  color: var(--premium-text-primary);\r\n  letter-spacing: 0.025em;\r\n}\r\n\r\n.premium-sublabel {\r\n  font-size: 0.75rem;\r\n  color: var(--premium-text-muted);\r\n  margin-top: 0.25rem;\r\n}\r\n\r\n.premium-number-input,\r\n.premium-select {\r\n  padding: 0.75rem 1rem;\r\n  border: 1px solid var(--premium-border);\r\n  border-radius: 0.5rem;\r\n  background: var(--premium-surface);\r\n  color: var(--premium-text-primary);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  transition: var(--transition-fast);\r\n  box-shadow: var(--shadow-soft);\r\n}\r\n\r\n.premium-number-input:focus,\r\n.premium-select:focus {\r\n  outline: none;\r\n  border-color: var(--premium-accent);\r\n  box-shadow: var(--shadow-glow);\r\n}\r\n\r\n.premium-toggle-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 1rem 1.25rem;\r\n  background: var(--premium-surface);\r\n  border: 1px solid var(--premium-border);\r\n  border-radius: 0.5rem;\r\n  box-shadow: var(--shadow-soft);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.premium-toggle-info {\r\n  flex-grow: 1;\r\n}\r\n\r\n.premium-toggle {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 3rem;\r\n  height: 1.75rem;\r\n}\r\n\r\n.premium-toggle-slider {\r\n  position: absolute;\r\n  cursor: pointer;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: var(--premium-border);\r\n  transition: var(--transition-normal);\r\n  border-radius: 1.75rem;\r\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.premium-toggle-slider:before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 1.25rem;\r\n  width: 1.25rem;\r\n  left: 0.25rem;\r\n  bottom: 0.25rem;\r\n  background: white;\r\n  transition: var(--transition-normal);\r\n  border-radius: 50%;\r\n  box-shadow: var(--shadow-soft);\r\n}\r\n\r\n.premium-toggle input:checked + .premium-toggle-slider {\r\n  background: var(--premium-accent);\r\n}\r\n\r\n.premium-toggle input:checked + .premium-toggle-slider:before {\r\n  transform: translateX(1.25rem);\r\n}\r\n\r\n/* Premium 3D Viewer */\r\n.premium-3d-container {\r\n  position: relative;\r\n}\r\n\r\n.premium-3d-badges {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.premium-style-badge {\r\n  padding: 0.5rem 1rem;\r\n  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\r\n  border: 1px solid #93c5fd;\r\n  border-radius: 0.5rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  color: var(--premium-accent-dark);\r\n}\r\n\r\n.premium-finish-badge {\r\n  padding: 0.5rem 1rem;\r\n  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 0.5rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  color: var(--premium-text-secondary);\r\n}\r\n\r\n.premium-3d-footer {\r\n  margin-top: 1rem;\r\n  padding: 1rem;\r\n  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);\r\n  border-radius: 0.75rem;\r\n  text-align: center;\r\n}\r\n\r\n.premium-3d-footer p {\r\n  font-size: 0.875rem;\r\n  color: var(--premium-text-muted);\r\n  font-weight: 500;\r\n}\r\n\r\n.premium-empty-3d {\r\n  min-height: 600px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.premium-empty-state {\r\n  width: 100%;\r\n}\r\n\r\n.premium-empty-icon {\r\n  color: var(--premium-text-muted);\r\n  opacity: 0.7;\r\n}\r\n\r\n.premium-requirements-card {\r\n  display: inline-block;\r\n  padding: 1.5rem;\r\n  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\r\n  border: 1px solid #bfdbfe;\r\n  border-radius: 1rem;\r\n  text-align: left;\r\n  margin-top: 1rem;\r\n}\r\n\r\n/* Premium Bill of Materials */\r\n.premium-spec-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0.5rem 1rem;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  border: 1px solid var(--premium-border);\r\n  border-radius: 0.5rem;\r\n  font-size: 0.875rem;\r\n  color: var(--premium-text-secondary);\r\n}\r\n\r\n.premium-table-container {\r\n  overflow-x: auto;\r\n  border-radius: 1rem;\r\n  border: 1px solid var(--premium-border);\r\n  background: var(--premium-surface);\r\n}\r\n\r\n.premium-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.premium-table th {\r\n  padding: 1.25rem 1rem;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  border-bottom: 2px solid var(--premium-border);\r\n  font-size: 0.875rem;\r\n  font-weight: 700;\r\n  color: var(--premium-text-primary);\r\n  text-align: left;\r\n  letter-spacing: 0.05em;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.premium-table td {\r\n  padding: 1.25rem 1rem;\r\n  border-bottom: 1px solid var(--premium-border-muted);\r\n  font-size: 0.875rem;\r\n  color: var(--premium-text-secondary);\r\n  vertical-align: top;\r\n}\r\n\r\n.premium-table tr:hover {\r\n  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);\r\n}\r\n\r\n.premium-model-number {\r\n  font-family: var(--font-mono);\r\n  font-size: 0.75rem;\r\n  padding: 0.5rem;\r\n  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);\r\n  border: 1px solid var(--premium-border);\r\n  border-radius: 0.5rem;\r\n  color: var(--premium-text-primary);\r\n  font-weight: 600;\r\n}\r\n\r\n.premium-category-badge {\r\n  display: inline-block;\r\n  padding: 0.375rem 0.75rem;\r\n  border-radius: 0.5rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  letter-spacing: 0.05em;\r\n  text-transform: uppercase;\r\n}\r\n\r\n/* Premium Animations */\r\n@keyframes premium-pulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n    opacity: 0.9;\r\n  }\r\n}\r\n\r\n@keyframes premium-bounce {\r\n  0%, 80%, 100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes premium-float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-10px);\r\n  }\r\n}\r\n\r\n/* Enhanced Responsive Design - Better Scaling */\r\n@media (max-width: 1920px) {\r\n  .premium-grid {\r\n    grid-template-columns: 460px 1fr 400px;\r\n    grid-gap: var(--space-8);\r\n  }\r\n}\r\n\r\n@media (max-width: 1600px) {\r\n  .premium-grid {\r\n    grid-template-columns: 440px 1fr 380px;\r\n    grid-gap: var(--space-6);\r\n  }\r\n\r\n  .premium-app-header .max-w-7xl,\r\n  .premium-app > .max-w-7xl {\r\n    padding: var(--space-6) var(--space-10);\r\n  }\r\n\r\n  .premium-title {\r\n    font-size: var(--text-3xl);\r\n  }\r\n}\r\n\r\n@media (max-width: 1280px) {\r\n  .premium-grid {\r\n    grid-template-columns: 1fr;\r\n    grid-template-areas: \r\n      \"viewer\"\r\n      \"chat\"\r\n      \"controls\"\r\n      \"bom\";\r\n    gap: 2rem;\r\n  }\r\n  \r\n  .premium-grid-chat,\r\n  .premium-grid-viewer,\r\n  .premium-grid-controls {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .premium-title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .premium-status-indicators {\r\n    flex-direction: column;\r\n    gap: 0.75rem;\r\n  }\r\n  \r\n  .premium-app-header .max-w-7xl,\r\n  .premium-app > .max-w-7xl {\r\n    padding: 0 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .premium-app-header .max-w-7xl {\r\n    padding: 1.5rem;\r\n  }\r\n  \r\n  .premium-app > .max-w-7xl {\r\n    padding: 1.5rem;\r\n  }\r\n  \r\n  .premium-title {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .premium-subtitle {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .premium-grid {\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .premium-card {\r\n    border-radius: 1.25rem;\r\n    min-height: auto;\r\n  }\r\n  \r\n  .premium-header {\r\n    padding: 1.5rem !important;\r\n  }\r\n  \r\n  .premium-section-required,\r\n  .premium-section-optional {\r\n    padding: 1.5rem;\r\n  }\r\n  \r\n  .premium-input-container {\r\n    padding: 1.5rem;\r\n  }\r\n}\r\n\r\n/* Premium Utilities */\r\n.premium-sr-only {\r\n  position: absolute;\r\n  width: 1px;\r\n  height: 1px;\r\n  padding: 0;\r\n  margin: -1px;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0);\r\n  white-space: nowrap;\r\n  border: 0;\r\n}\r\n\r\n.premium-transition {\r\n  transition: var(--transition-normal);\r\n}\r\n\r\n.premium-shadow-glow {\r\n  box-shadow: var(--shadow-glow);\r\n}\r\n\r\n/* Print Styles */\r\n@media print {\r\n  .premium-app {\r\n    background: white !important;\r\n  }\r\n  \r\n  .premium-card {\r\n    box-shadow: none !important;\r\n    border: 1px solid #e5e7eb !important;\r\n  }\r\n  \r\n  .no-print {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n/* High Contrast Mode */\r\n@media (prefers-contrast: high) {\r\n  :root {\r\n    --premium-border: #000000;\r\n    --premium-text-secondary: #000000;\r\n  }\r\n}\r\n\r\n/* Reduced Motion */\r\n@media (prefers-reduced-motion: reduce) {\r\n  * {\r\n    animation-duration: 0.01ms !important;\r\n    animation-iteration-count: 1 !important;\r\n    transition-duration: 0.01ms !important;\r\n    scroll-behavior: auto !important;\r\n  }\r\n}\r\n\r\n/* Dark Mode Support (Future Enhancement) */\r\n@media (prefers-color-scheme: dark) {\r\n  /* Dark mode variables can be added here in future versions */\r\n}"], "names": [], "sourceRoot": ""}