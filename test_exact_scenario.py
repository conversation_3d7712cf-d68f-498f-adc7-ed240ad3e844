#!/usr/bin/env python3

import requests
import json
import uuid

def test_exact_user_scenario():
    """Test the exact scenario described by the user"""
    base_url = "http://localhost:8002/api"
    session_id = f"user_scenario_{uuid.uuid4()}"
    
    print("🧪 Testing EXACT user scenario:")
    print("User says they provided: Width: 36 inches, Length: 18 inches, PostHeight: 36 inches, NumberOfShelves: 2")
    print("AI claims: 'I have enough information to create your 3D model'")
    print("But 3D model was NOT rendered!")
    print(f"\nSession ID: {session_id}")
    
    # Simulate the conversation that led to the issue
    conversation = [
        "I need a wire shelf",
        "36 inches wide",
        "18 inches long", 
        "36 inches tall",
        "2 shelves",
        "That's all the information"
    ]
    
    for i, message in enumerate(conversation, 1):
        print(f"\n--- Step {i} ---")
        print(f"👤 User: {message}")
        
        try:
            response = requests.post(f"{base_url}/chat", json={
                "message": message,
                "session_id": session_id
            })
            
            if response.status_code == 200:
                data = response.json()
                print(f"🤖 AI: {data['response'][:100]}...")
                
                entities = data['extracted_entities']
                has_sufficient = data['has_sufficient_entities']
                
                print(f"📊 Extracted: {entities}")
                print(f"✅ Sufficient: {has_sufficient}")
                
                # Check for the specific issue
                required_keys = ['width', 'length', 'postHeight', 'numberOfShelves']
                has_all_required = all(key in entities and entities[key] is not None for key in required_keys)
                
                print(f"🎯 Has all required for 3D: {has_all_required}")
                
                if has_all_required:
                    print("🎉 SUCCESS: Should render 3D model!")
                    print(f"   Width: {entities['width']}")
                    print(f"   Length: {entities['length']}")
                    print(f"   Height: {entities['postHeight']}")
                    print(f"   Shelves: {entities['numberOfShelves']}")
                    
                    # Check if AI is claiming it has enough info
                    if "enough information" in data['response'].lower() or "create your 3d model" in data['response'].lower():
                        print("✅ AI correctly claims it has enough info")
                        print("🔍 If 3D model is not rendering, the issue is in the frontend!")
                    
                elif has_sufficient:
                    print("⚠️  WARNING: Backend says sufficient but missing required keys!")
                    missing = [key for key in required_keys if key not in entities or entities[key] is None]
                    print(f"   Missing: {missing}")
                    
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    print("\n" + "="*60)
    print("🔍 DIAGNOSIS:")
    print("If the backend correctly extracts all entities and returns has_sufficient_entities=True,")
    print("but the 3D model doesn't render, then the issue is in the frontend React component.")
    print("Check the browser console for errors and verify the frontend state management.")

if __name__ == "__main__":
    test_exact_user_scenario()
