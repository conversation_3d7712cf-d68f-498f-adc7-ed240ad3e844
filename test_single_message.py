#!/usr/bin/env python3

import requests
import json
import uuid

def test_single_message():
    """Test with a single comprehensive message"""
    base_url = "http://localhost:8002/api"
    session_id = f"single_test_{uuid.uuid4()}"
    
    print(f"🧪 Testing single comprehensive message")
    print(f"Session ID: {session_id}")
    
    message = "I need a wire shelf that is 36 inches wide, 18 inches long, 36 inches tall with 2 shelves"
    
    print(f"\n👤 User: {message}")
    
    try:
        response = requests.post(f"{base_url}/chat", json={
            "message": message,
            "session_id": session_id
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n🤖 AI Response:")
            print(f"{data['response']}")
            
            print(f"\n📊 API Response Data:")
            print(f"Extracted entities: {json.dumps(data['extracted_entities'], indent=2)}")
            print(f"Has sufficient entities: {data['has_sufficient_entities']}")
            
            # Check for 3D model requirements
            entities = data['extracted_entities']
            required_keys = ['width', 'length', 'postHeight', 'numberOfShelves']
            has_all_required = all(key in entities and entities[key] is not None for key in required_keys)
            
            print(f"\n🎯 3D Model Check:")
            print(f"Has all required keys: {has_all_required}")
            print(f"Required keys present: {[key for key in required_keys if key in entities]}")
            print(f"Missing keys: {[key for key in required_keys if key not in entities]}")
            
            if has_all_required:
                print(f"\n✅ SUCCESS: Should render 3D model!")
                print(f"Parameters for 3D model:")
                for key in required_keys:
                    print(f"  - {key}: {entities[key]}")
            else:
                print(f"\n❌ ISSUE: Missing required parameters")
                
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"💥 Exception: {e}")

if __name__ == "__main__":
    test_single_message()
