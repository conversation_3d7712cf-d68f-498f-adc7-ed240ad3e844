from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json
import re
import uuid
import os
import asyncio
from dotenv import load_dotenv
from cerebras.cloud.sdk import Cerebras

# Load environment variables
load_dotenv()

# Cerebras implementation
class UserMessage:
    def __init__(self, text):
        self.text = text

class LlmChat:
    def __init__(self, api_key, session_id, system_message):
        self.api_key = api_key
        self.session_id = session_id
        self.system_message = system_message
        self.model = None
        self.client = Cerebras(api_key=api_key)
        self.conversation_history = [
            {"role": "system", "content": system_message}
        ]

    def with_model(self, provider, model_name):
        self.model = model_name  # Use the model name directly for Ce<PERSON>bras
        return self

    async def send_message(self, user_message):
        try:
            # Add user message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": user_message.text
            })

            print(f"Sending message to Cerebras: {user_message.text}")

            # Create chat completion with Cerebras
            response = self.client.chat.completions.create(
                messages=self.conversation_history,
                model="llama-4-scout-17b-16e-instruct",
                max_completion_tokens=4196,
                temperature=0,
                top_p=0.9
            )

            # Extract the response content
            ai_response = response.choices[0].message.content
            print(f"Cerebras response: {ai_response}")

            # Add AI response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": ai_response
            })

            return ai_response

        except Exception as e:
            print(f"Error with Cerebras API: {str(e)}")
            import traceback
            traceback.print_exc()

            # Enhanced fallback response with entity extraction
            user_text = user_message.text.lower()

            # Try to extract basic dimensions from user input
            width_match = re.search(r'(\d+)\s*(?:inch|in|inches)?\s*(?:wide|width)', user_text)
            length_match = re.search(r'(\d+)\s*(?:inch|in|inches)?\s*(?:deep|depth|long|length)', user_text)
            height_match = re.search(r'(\d+)\s*(?:inch|in|inches)?\s*(?:tall|high|height)', user_text)
            shelves_match = re.search(r'(\d+)\s*(?:shelf|shelves|levels?)', user_text)

            extracted = {}
            if width_match:
                extracted["width"] = int(width_match.group(1))
            if length_match:
                extracted["length"] = int(length_match.group(1))
            if height_match:
                extracted["postHeight"] = int(height_match.group(1))
            if shelves_match:
                extracted["numberOfShelves"] = int(shelves_match.group(1))

            response_parts = ["I understand you're looking for wire shelving."]

            if extracted:
                response_parts.append("I've noted these specifications:")
                for key, value in extracted.items():
                    if key == "width":
                        response_parts.append(f"- Width: {value} inches")
                    elif key == "length":
                        response_parts.append(f"- Depth: {value} inches")
                    elif key == "postHeight":
                        response_parts.append(f"- Height: {value} inches")
                    elif key == "numberOfShelves":
                        response_parts.append(f"- Number of shelves: {value}")

            missing = []
            if "width" not in extracted:
                missing.append("Width (how wide?)")
            if "length" not in extracted:
                missing.append("Depth (how deep?)")
            if "postHeight" not in extracted:
                missing.append("Height (how tall?)")
            if "numberOfShelves" not in extracted:
                missing.append("Number of shelves")

            if missing:
                response_parts.append(f"\nI still need: {', '.join(missing)}")

            response_text = " ".join(response_parts)

            return f"""{response_text}

ENTITIES_EXTRACTED: {json.dumps(extracted)}
SUFFICIENT: {str(len(extracted) >= 4).lower()}"""

app = FastAPI()

# Allow CORS for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for chat sessions
chat_sessions = {}
chat_histories = {}

class ChatMessage(BaseModel):
    message: str
    session_id: str

class ChatResponse(BaseModel):
    response: str
    extracted_entities: Dict[str, Any]
    has_sufficient_entities: bool
    next_questions: List[str]

@app.get("/")
async def root():
    return {"message": "Wire Shelves 3D Configurator API"}

@app.get("/api/")
async def api_root():
    return {"message": "Wire Shelves 3D Configurator API"}

@app.post("/api/chat")
async def chat_with_ai(chat_request: ChatMessage):
    try:
        session_id = chat_request.session_id
        user_message = chat_request.message
        
        # Initialize chat session if not exists
        if session_id not in chat_sessions:
            system_message = """You are a professional wire shelf designer assistant. Your role is to engage customers in natural conversation to understand their wire shelf requirements and extract key entities needed for design.

🚨 CRITICAL: YOU MUST ALWAYS END YOUR RESPONSE WITH THE EXACT JSON FORMAT BELOW. NO EXCEPTIONS! 🚨

=== COMPLETE JSON SCHEMA SPECIFICATION ===

You MUST output a JSON object with EXACTLY these fields and ONLY these allowed values:

REQUIRED FIELDS (needed for 3D model):
{
  "width": <integer 12-96>,           // Shelf width in inches, range 12-96
  "length": <integer 12-96>,          // Shelf depth in inches, range 12-96
  "postHeight": <integer 24-84>,      // Overall height in inches, range 24-84
  "numberOfShelves": <integer 2-8>    // Number of shelf levels, range 2-8
}

OPTIONAL FIELDS:
{
  "color": <string>,                  // ONLY: "Chrome" | "Black" | "White" | "Stainless Steel" | "Bronze"
  "shelfStyle": <string>,             // ONLY: "Industrial Grid" | "Metro Classic" | "Commercial Pro" | "Heavy Duty"
  "solidBottomShelf": <boolean>,      // true or false
  "postType": <string>,               // ONLY: "Stationary" | "Mobile"
  "accessories": {
    "shelfDividers": {
      "enabled": <boolean>,           // true or false
      "quantity": <integer 1-6>,      // Number of dividers, range 1-6
      "shelves": <array of integers>  // Array of shelf indices [0, 1, 2, etc.]
    },
    "stackableLedges": {
      "enabled": <boolean>,           // true or false
      "position": <string>,           // ONLY: "front" | "back" | "both"
      "shelves": <array of integers>  // Array of shelf indices [0, 1, 2, etc.]
    },
    "enclosurePanels": {
      "enabled": <boolean>,           // true or false
      "sides": <boolean>,             // true or false
      "back": <boolean>,              // true or false
      "partial": <boolean>            // true or false
    }
  }
}

=== CONVERSATION FLOW ===
1. ALWAYS check the "Current extracted entities" provided in each message
2. If entities are already extracted, acknowledge them and build upon them
3. Extract the 4 REQUIRED entities: width, length, postHeight, numberOfShelves
4. Only when all 4 are collected, mention "I have enough information to create your 3D model"
5. Then gather optional details following the schema above
6. Be conversational like a designer talking to a customer
7. Extract numbers and dimensions from natural language

=== MANDATORY RESPONSE FORMAT ===
[Your conversational response here]

ENTITIES_EXTRACTED: {JSON object following the schema above}
SUFFICIENT: true

=== EXAMPLES ===

Example 1 - Basic required fields:
ENTITIES_EXTRACTED: {"width": 36, "length": 18, "postHeight": 36, "numberOfShelves": 2}
SUFFICIENT: true

Example 2 - With optional fields:
ENTITIES_EXTRACTED: {"width": 48, "length": 24, "postHeight": 72, "numberOfShelves": 4, "color": "Chrome", "shelfStyle": "Industrial Grid", "solidBottomShelf": true, "postType": "Mobile"}
SUFFICIENT: true

Example 3 - With accessories:
ENTITIES_EXTRACTED: {"width": 36, "length": 18, "postHeight": 36, "numberOfShelves": 2, "accessories": {"shelfDividers": {"enabled": true, "quantity": 2, "shelves": [0, 1]}, "stackableLedges": {"enabled": true, "position": "front", "shelves": [0]}}}
SUFFICIENT: true

🚨 VALIDATION RULES 🚨
- Use ONLY the exact field names and values specified in the schema
- All integers must be within the specified ranges
- All strings must match the exact allowed values (case-sensitive)
- Arrays must contain only valid integers
- Boolean values must be exactly true or false (not "true" or "false")
- Do NOT add any fields not in the schema
- Do NOT use different field names or values

REMEMBER: ALWAYS include ENTITIES_EXTRACTED and SUFFICIENT at the end of your response!"""

            chat_sessions[session_id] = LlmChat(
                api_key=os.getenv("CEREBRAS_API_KEY"),
                session_id=session_id,
                system_message=system_message
            ).with_model("cerebras", "llama-4-scout-17b-16e-instruct")
            
            chat_histories[session_id] = []

        # Get chat instance
        chat = chat_sessions[session_id]

        # Get current extracted entities for this session
        current_entities = getattr(extract_entities_from_response, f'entities_{session_id}', {})

        # Create context-aware message that includes current state
        context_message = f"""Current extracted entities: {json.dumps(current_entities)}

User message: {user_message}

Please respond considering the entities already extracted above. If all 4 required entities (width, length, postHeight, numberOfShelves) are already extracted, acknowledge this and help with optional features or accessories."""

        # Send message to AI
        user_msg = UserMessage(text=context_message)
        ai_response = await chat.send_message(user_msg)

        # Check if AI response contains required format
        if not re.search(r'ENTITIES_EXTRACTED:', ai_response, re.IGNORECASE):
            print("DEBUG: AI response missing ENTITIES_EXTRACTED format, requesting correction...")

            # Force the AI to provide the correct format
            correction_msg = UserMessage(text="""You must end your response with the exact format:

ENTITIES_EXTRACTED: {json object}
SUFFICIENT: {true or false}

Please provide your previous response again with this required format at the end.""")

            ai_response = await chat.send_message(correction_msg)
            print(f"DEBUG: Corrected AI response: {ai_response}")

        # Store message history
        chat_histories[session_id].append({"type": "user", "content": user_message})
        chat_histories[session_id].append({"type": "ai", "content": ai_response})

        # Extract entities and sufficient status from AI response
        extracted_entities = extract_entities_from_response(ai_response, session_id)
        has_sufficient = check_sufficient_entities(extracted_entities)

        print(f"DEBUG: Final extracted entities: {extracted_entities}")
        print(f"DEBUG: Has sufficient entities: {has_sufficient}")

        # Clean the response (remove the ENTITIES_EXTRACTED part)
        clean_response = clean_ai_response(ai_response)
        
        return ChatResponse(
            response=clean_response,
            extracted_entities=extracted_entities,
            has_sufficient_entities=has_sufficient,
            next_questions=generate_next_questions(extracted_entities, has_sufficient)
        )
        
    except Exception as e:
        print(f"Error in chat: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

def validate_json_schema(entities: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and clean entities according to the JSON schema"""
    validated = {}

    # Validate required fields with ranges
    if 'width' in entities:
        width = entities['width']
        if isinstance(width, (int, float)) and 12 <= width <= 96:
            validated['width'] = int(width)

    if 'length' in entities:
        length = entities['length']
        if isinstance(length, (int, float)) and 12 <= length <= 96:
            validated['length'] = int(length)

    if 'postHeight' in entities:
        height = entities['postHeight']
        if isinstance(height, (int, float)) and 24 <= height <= 84:
            validated['postHeight'] = int(height)

    if 'numberOfShelves' in entities:
        shelves = entities['numberOfShelves']
        if isinstance(shelves, (int, float)) and 2 <= shelves <= 8:
            validated['numberOfShelves'] = int(shelves)

    # Validate optional string fields with exact values
    valid_colors = ["Chrome", "Black", "White", "Stainless Steel", "Bronze"]
    if 'color' in entities and entities['color'] in valid_colors:
        validated['color'] = entities['color']

    valid_styles = ["Industrial Grid", "Metro Classic", "Commercial Pro", "Heavy Duty"]
    if 'shelfStyle' in entities and entities['shelfStyle'] in valid_styles:
        validated['shelfStyle'] = entities['shelfStyle']

    valid_post_types = ["Stationary", "Mobile"]
    if 'postType' in entities and entities['postType'] in valid_post_types:
        validated['postType'] = entities['postType']

    # Validate boolean fields
    if 'solidBottomShelf' in entities and isinstance(entities['solidBottomShelf'], bool):
        validated['solidBottomShelf'] = entities['solidBottomShelf']

    # Validate accessories
    if 'accessories' in entities and isinstance(entities['accessories'], dict):
        validated['accessories'] = {}
        accessories = entities['accessories']

        # Validate shelfDividers
        if 'shelfDividers' in accessories and isinstance(accessories['shelfDividers'], dict):
            dividers = accessories['shelfDividers']
            validated_dividers = {}

            if 'enabled' in dividers and isinstance(dividers['enabled'], bool):
                validated_dividers['enabled'] = dividers['enabled']

            if 'quantity' in dividers and isinstance(dividers['quantity'], (int, float)) and 1 <= dividers['quantity'] <= 6:
                validated_dividers['quantity'] = int(dividers['quantity'])

            if 'shelves' in dividers and isinstance(dividers['shelves'], list):
                valid_shelves = [s for s in dividers['shelves'] if isinstance(s, (int, float)) and s >= 0]
                validated_dividers['shelves'] = [int(s) for s in valid_shelves]

            if validated_dividers:
                validated['accessories']['shelfDividers'] = validated_dividers

        # Validate stackableLedges
        if 'stackableLedges' in accessories and isinstance(accessories['stackableLedges'], dict):
            ledges = accessories['stackableLedges']
            validated_ledges = {}

            if 'enabled' in ledges and isinstance(ledges['enabled'], bool):
                validated_ledges['enabled'] = ledges['enabled']

            valid_positions = ["front", "back", "both"]
            if 'position' in ledges and ledges['position'] in valid_positions:
                validated_ledges['position'] = ledges['position']

            if 'shelves' in ledges and isinstance(ledges['shelves'], list):
                valid_shelves = [s for s in ledges['shelves'] if isinstance(s, (int, float)) and s >= 0]
                validated_ledges['shelves'] = [int(s) for s in valid_shelves]

            if validated_ledges:
                validated['accessories']['stackableLedges'] = validated_ledges

        # Validate enclosurePanels
        if 'enclosurePanels' in accessories and isinstance(accessories['enclosurePanels'], dict):
            panels = accessories['enclosurePanels']
            validated_panels = {}

            for field in ['enabled', 'sides', 'back', 'partial']:
                if field in panels and isinstance(panels[field], bool):
                    validated_panels[field] = panels[field]

            if validated_panels:
                validated['accessories']['enclosurePanels'] = validated_panels

    return validated

def extract_entities_from_response(response: str, session_id: str) -> Dict[str, Any]:
    """Extract entities from AI response and maintain session state"""
    # Get existing entities for this session
    existing_entities = getattr(extract_entities_from_response, f'entities_{session_id}', {})

    print(f"DEBUG: Raw AI response for session {session_id}:")
    print(f"'{response}'")

    # Try to extract from the structured response with multiple patterns
    entities_match = None

    # Pattern 1: Standard format
    entities_match = re.search(r'ENTITIES_EXTRACTED:\s*(\{[^}]*\})', response, re.IGNORECASE)

    # Pattern 2: Multi-line JSON
    if not entities_match:
        entities_match = re.search(r'ENTITIES_EXTRACTED:\s*(\{.*?\})', response, re.IGNORECASE | re.DOTALL)

    # Pattern 3: Look for any JSON-like structure after ENTITIES_EXTRACTED
    if not entities_match:
        entities_match = re.search(r'ENTITIES_EXTRACTED:\s*([^\\n]*)', response, re.IGNORECASE)

    if entities_match:
        json_str = entities_match.group(1).strip()
        print(f"DEBUG: Extracted JSON string: '{json_str}'")

        try:
            # Clean up the JSON string
            json_str = json_str.replace('\n', '').replace('\r', '')
            new_entities = json.loads(json_str)
            print(f"DEBUG: Parsed entities: {new_entities}")

            # Validate against schema
            validated_entities = validate_json_schema(new_entities)
            print(f"DEBUG: Validated entities: {validated_entities}")

            existing_entities.update(validated_entities)
        except json.JSONDecodeError as e:
            print(f"DEBUG: JSON decode error: {e}")
            print(f"DEBUG: Failed to parse: '{json_str}'")
            # Try to fix common JSON issues
            try:
                # Fix single quotes to double quotes
                json_str = json_str.replace("'", '"')
                # Fix trailing commas
                json_str = re.sub(r',\s*}', '}', json_str)
                json_str = re.sub(r',\s*]', ']', json_str)
                new_entities = json.loads(json_str)
                print(f"DEBUG: Fixed and parsed entities: {new_entities}")

                # Validate against schema
                validated_entities = validate_json_schema(new_entities)
                print(f"DEBUG: Validated fixed entities: {validated_entities}")

                existing_entities.update(validated_entities)
            except json.JSONDecodeError as e2:
                print(f"DEBUG: Still failed after fixes: {e2}")
                pass
    
    # Also extract from natural language in the response
    text = response.lower()
    
    # Extract dimensions
    width_match = re.search(r'(\d+)\s*(?:inch|inches|in|"|''|′)?\s*(?:wide|width)', text)
    if width_match:
        existing_entities['width'] = int(width_match.group(1))
    
    length_match = re.search(r'(\d+)\s*(?:inch|inches|in|"|''|′)?\s*(?:long|length|deep|depth)', text)
    if length_match:
        existing_entities['length'] = int(length_match.group(1))
    
    height_match = re.search(r'(\d+)\s*(?:inch|inches|in|"|''|′)?\s*(?:tall|high|height)', text)
    if height_match:
        existing_entities['postHeight'] = int(height_match.group(1))
    
    shelves_match = re.search(r'(\d+)\s*(?:shelf|shelves|level|levels|tier|tiers)', text)
    if shelves_match:
        existing_entities['numberOfShelves'] = int(shelves_match.group(1))
    
    # Extract colors
    colors = ['chrome', 'black', 'white', 'stainless steel', 'bronze']
    for color in colors:
        if color in text:
            existing_entities['color'] = color.title()
    
    # Extract styles
    styles = ['industrial grid', 'metro classic', 'commercial pro', 'heavy duty']
    for style in styles:
        if style in text:
            existing_entities['shelfStyle'] = style.title()
    
    # Extract post type
    if 'mobile' in text or 'caster' in text or 'wheel' in text:
        existing_entities['postType'] = 'Mobile'
    elif 'stationary' in text or 'fixed' in text:
        existing_entities['postType'] = 'Stationary'
    
    # Extract solid bottom shelf
    if 'solid bottom' in text or 'solid shelf' in text:
        existing_entities['solidBottomShelf'] = True
    elif 'wire bottom' in text or 'no solid' in text:
        existing_entities['solidBottomShelf'] = False

    # Extract accessories
    if 'accessories' not in existing_entities:
        existing_entities['accessories'] = {}

    # Shelf dividers
    if 'divider' in text or 'dividers' in text:
        if 'shelfDividers' not in existing_entities['accessories']:
            existing_entities['accessories']['shelfDividers'] = {}
        existing_entities['accessories']['shelfDividers']['enabled'] = True

        # Extract quantity
        divider_qty_match = re.search(r'(\d+)\s*(?:divider|dividers)', text)
        if divider_qty_match:
            existing_entities['accessories']['shelfDividers']['quantity'] = int(divider_qty_match.group(1))

    # Stackable ledges
    if 'ledge' in text or 'ledges' in text or 'edge guard' in text:
        if 'stackableLedges' not in existing_entities['accessories']:
            existing_entities['accessories']['stackableLedges'] = {}
        existing_entities['accessories']['stackableLedges']['enabled'] = True

        if 'front' in text and 'back' in text:
            existing_entities['accessories']['stackableLedges']['position'] = 'both'
        elif 'front' in text:
            existing_entities['accessories']['stackableLedges']['position'] = 'front'
        elif 'back' in text:
            existing_entities['accessories']['stackableLedges']['position'] = 'back'

    # Enclosure panels
    if 'enclosure' in text or 'panel' in text or 'panels' in text:
        if 'enclosurePanels' not in existing_entities['accessories']:
            existing_entities['accessories']['enclosurePanels'] = {}
        existing_entities['accessories']['enclosurePanels']['enabled'] = True

        if 'side' in text or 'sides' in text:
            existing_entities['accessories']['enclosurePanels']['sides'] = True
        if 'back' in text:
            existing_entities['accessories']['enclosurePanels']['back'] = True
        if 'top' in text:
            existing_entities['accessories']['enclosurePanels']['back'] = True  # Treat "top" as back panel

    # Store entities for this session
    setattr(extract_entities_from_response, f'entities_{session_id}', existing_entities)

    return existing_entities

def check_sufficient_entities(entities: Dict[str, Any]) -> bool:
    """Check if we have all required entities for 3D model"""
    required = ['width', 'length', 'postHeight', 'numberOfShelves']
    return all(key in entities and entities[key] is not None for key in required)

def clean_ai_response(response: str) -> str:
    """Remove the ENTITIES_EXTRACTED and SUFFICIENT parts from response"""
    # Remove ENTITIES_EXTRACTED section
    cleaned = re.sub(r'ENTITIES_EXTRACTED:.*$', '', response, flags=re.IGNORECASE | re.DOTALL)
    cleaned = re.sub(r'SUFFICIENT:.*$', '', cleaned, flags=re.IGNORECASE | re.DOTALL)
    
    return cleaned.strip()

def generate_next_questions(entities: Dict[str, Any], has_sufficient: bool) -> List[str]:
    """Generate helpful next questions based on current state"""
    if has_sufficient:
        questions = []
        if 'color' not in entities:
            questions.append("What color/finish would you prefer?")
        if 'shelfStyle' not in entities:
            questions.append("Do you have a preference for shelf style?")
        if 'solidBottomShelf' not in entities:
            questions.append("Would you like a solid bottom shelf?")
        if 'postType' not in entities:
            questions.append("Do you need this to be mobile with casters?")
        
        if not questions:
            questions.append("Is there anything you'd like to adjust about your shelving unit?")
        
        return questions
    else:
        missing = []
        if 'width' not in entities:
            missing.append("How wide should it be?")
        if 'length' not in entities:
            missing.append("How deep/long should it be?")
        if 'postHeight' not in entities:
            missing.append("How tall should it be?")
        if 'numberOfShelves' not in entities:
            missing.append("How many shelves do you need?")
        
        return missing

@app.get("/api/chat/history/{session_id}")
async def get_chat_history(session_id: str):
    """Get chat history for a session"""
    return chat_histories.get(session_id, [])

@app.delete("/api/chat/{session_id}")
async def clear_chat_session(session_id: str):
    """Clear a chat session"""
    if session_id in chat_sessions:
        del chat_sessions[session_id]
    if session_id in chat_histories:
        del chat_histories[session_id]
    # Clear stored entities
    if hasattr(extract_entities_from_response, f'entities_{session_id}'):
        delattr(extract_entities_from_response, f'entities_{session_id}')

    return {"message": "Session cleared"}

@app.post("/api/test-entities")
async def test_entity_extraction():
    """Test endpoint to verify entity extraction is working"""
    test_session = "test_session_" + str(uuid.uuid4())

    # Test with a complete set of entities
    test_response = """Great! I have all the information I need to create your 3D model. Let me confirm:
    - Width: 36 inches
    - Length: 18 inches
    - Height: 36 inches
    - Number of shelves: 2

    ENTITIES_EXTRACTED: {"width": 36, "length": 18, "postHeight": 36, "numberOfShelves": 2}
    SUFFICIENT: true"""

    extracted = extract_entities_from_response(test_response, test_session)
    sufficient = check_sufficient_entities(extracted)

    return {
        "test_response": test_response,
        "extracted_entities": extracted,
        "has_sufficient": sufficient,
        "expected_sufficient": True,
        "test_passed": sufficient == True and len(extracted) >= 4
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
