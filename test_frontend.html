<!DOCTYPE html>
<html>
<head>
    <title>Frontend Test</title>
</head>
<body>
    <h1>Frontend API Test</h1>
    <button onclick="testChat()">Test Chat API</button>
    <div id="results"></div>

    <script>
        async function testChat() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:8002/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: "I need a wire shelf that is 36 inches wide, 18 inches long, 36 inches tall with 2 shelves",
                        session_id: "test_frontend_" + Date.now()
                    })
                });
                
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h2>API Response:</h2>
                    <p><strong>AI Response:</strong> ${data.response}</p>
                    <p><strong>Extracted Entities:</strong> ${JSON.stringify(data.extracted_entities, null, 2)}</p>
                    <p><strong>Has Sufficient:</strong> ${data.has_sufficient_entities}</p>
                    
                    <h2>3D Model Check:</h2>
                    <p><strong>Width:</strong> ${data.extracted_entities.width}</p>
                    <p><strong>Length:</strong> ${data.extracted_entities.length}</p>
                    <p><strong>Height:</strong> ${data.extracted_entities.postHeight}</p>
                    <p><strong>Shelves:</strong> ${data.extracted_entities.numberOfShelves}</p>
                    
                    <p><strong>Should render 3D model:</strong> ${
                        data.extracted_entities.width && 
                        data.extracted_entities.length && 
                        data.extracted_entities.postHeight && 
                        data.extracted_entities.numberOfShelves ? 'YES ✅' : 'NO ❌'
                    }</p>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
