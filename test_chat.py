#!/usr/bin/env python3

import requests
import json
import uuid

# Test the chat API with the exact scenario from the user's issue
def test_chat_scenario():
    base_url = "http://localhost:8002/api"
    session_id = f"test_session_{uuid.uuid4()}"
    
    print(f"Testing with session ID: {session_id}")
    
    # Test messages that should result in a 3D model
    messages = [
        "I need a wire shelf that is 36 inches wide, 18 inches long, 36 inches tall with 2 shelves",
        "Yes, that's perfect. I have all the dimensions now."
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n--- Message {i} ---")
        print(f"User: {message}")
        
        try:
            response = requests.post(f"{base_url}/chat", json={
                "message": message,
                "session_id": session_id
            })
            
            if response.status_code == 200:
                data = response.json()
                print(f"AI: {data['response']}")
                print(f"Extracted entities: {data['extracted_entities']}")
                print(f"Has sufficient entities: {data['has_sufficient_entities']}")
                
                # Check if we have minimum required parameters
                entities = data['extracted_entities']
                has_minimum = all(key in entities for key in ['width', 'length', 'postHeight', 'numberOfShelves'])
                print(f"Has minimum params for 3D model: {has_minimum}")
                
                if has_minimum:
                    print("✅ SUCCESS: Should render 3D model!")
                    print(f"3D Model Parameters:")
                    print(f"  - Width: {entities.get('width')} inches")
                    print(f"  - Length: {entities.get('length')} inches") 
                    print(f"  - Height: {entities.get('postHeight')} inches")
                    print(f"  - Shelves: {entities.get('numberOfShelves')}")
                else:
                    print("❌ ISSUE: Missing required parameters for 3D model")
                    missing = [key for key in ['width', 'length', 'postHeight', 'numberOfShelves'] if key not in entities]
                    print(f"Missing: {missing}")
                    
            else:
                print(f"Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"Exception: {e}")

if __name__ == "__main__":
    test_chat_scenario()
