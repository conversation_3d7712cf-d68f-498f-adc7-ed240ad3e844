// Test script to verify frontend data handling
// Run this in the browser console on the React app

// Simulate the exact API response format
const mockApiResponse = {
  response: "Great! I have all the information I need to create your 3D model.",
  extracted_entities: {
    width: 36,
    length: 18,
    postHeight: 36,
    numberOfShelves: 2,
    color: "Bronze",
    accessories: {}
  },
  has_sufficient_entities: true,
  next_questions: []
};

console.log('🧪 Testing frontend data handling...');
console.log('Mock API Response:', mockApiResponse);

// Test the hasMinimumParams logic
const entities = mockApiResponse.extracted_entities;
const hasMinimumParams = entities.width && entities.length && entities.postHeight && entities.numberOfShelves;

console.log('📊 Entity values:');
console.log('  width:', entities.width, typeof entities.width);
console.log('  length:', entities.length, typeof entities.length);
console.log('  postHeight:', entities.postHeight, typeof entities.postHeight);
console.log('  numberOfShelves:', entities.numberOfShelves, typeof entities.numberOfShelves);

console.log('🎯 hasMinimumParams calculation:', hasMinimumParams);

if (hasMinimumParams) {
  console.log('✅ Should render 3D model!');
} else {
  console.log('❌ Will NOT render 3D model');
  console.log('Missing values:');
  if (!entities.width) console.log('  - width is falsy');
  if (!entities.length) console.log('  - length is falsy');
  if (!entities.postHeight) console.log('  - postHeight is falsy');
  if (!entities.numberOfShelves) console.log('  - numberOfShelves is falsy');
}

// Test if we can find the React app's state
console.log('🔍 Looking for React app state...');
try {
  // Try to find React components in the DOM
  const reactRoot = document.querySelector('#root');
  if (reactRoot && reactRoot._reactInternalFiber) {
    console.log('Found React fiber');
  } else if (reactRoot && reactRoot._reactInternalInstance) {
    console.log('Found React instance');
  } else {
    console.log('React state not accessible from console');
  }
} catch (e) {
  console.log('Error accessing React state:', e.message);
}

console.log('✅ Test complete. Check the browser console for any errors.');
